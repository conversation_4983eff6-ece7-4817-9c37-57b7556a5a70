<?php

namespace S<PERSON>qi\PartialShipment;

use Shaqi\PluginManagement\Abstracts\PluginOperationAbstract;
use Illuminate\Support\Facades\Schema;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        Schema::dropIfExists('ec_partial_shipment_items');
        Schema::dropIfExists('ec_partial_shipments');
        
        // Remove added columns from order products table
        if (Schema::hasColumn('ec_order_product', 'qty_shipped')) {
            Schema::table('ec_order_product', function ($table) {
                $table->dropColumn(['qty_shipped', 'qty_remaining']);
            });
        }
    }
}
