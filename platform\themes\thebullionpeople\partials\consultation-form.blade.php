    {!! Form::open(['route' => 'public.send.consultation-form', 'method' => 'POST', 'class' => 'contact-form']) !!}

    {!! apply_filters('pre_contact_form', null) !!}

    <div class="row">

        <div class="col-lg-12">
            <label>First Name</label>
            <input type="text" name="first_name" id="first_name" class="form-control" placeholder="" required>
        </div>

        <div class="col-lg-12">
            <label>Last Name</label>
            <input type="text" name="last_name" id="last_name" class="form-control" placeholder="" required>
        </div>

        <div class="col-lg-12">
            <label>Email</label>
            <input type="text" name="email" id="email" class="form-control" placeholder="" required>
        </div>

        <div class="col-lg-12">
            <label>Phone</label>
            <input type="text" name="phone" id="phone" class="form-control" placeholder="" required>
        </div>

        <div class="col-lg-12">
            <label>Best time for me is:</label>
            <select class="form-control" name="best_time" id="best_time" required>
                <option value="">Select</option>
                @php
                    $bestTimes = [];
                    $start = strtotime('10:00 AM');
                    $end = strtotime('4:00 PM');
                    while ($start < $end) {
                        $endTime = strtotime('+30 minutes', $start);
                        $bestTimes[] = [
                            'value' => date('g:i A', $start) . ' - ' . date('g:i A', $endTime),
                            'label' => date('g:i A', $start) . ' - ' . date('g:i A', $endTime)
                        ];
                        $start = $endTime;
                    }
                @endphp
                @foreach ($bestTimes as $time)
                    <option value="{{ $time['value'] }}">{{ $time['label'] }}</option>
                @endforeach

            </select>


        </div>

    </div>

    <div class="field-set mb-4">
        <label>Message</label>
        <textarea name="message" id="message" class="form-control" placeholder="Your Message"></textarea>
    </div>


    @if (is_plugin_active('captcha'))
        @if (Captcha::reCaptchaEnabled())
            <div class="field-set mb-4">
                <div class="contact-form-group">
                    {!! Captcha::display() !!}
                </div>
            </div>
        @endif

        @if (Captcha::mathCaptchaEnabled() && setting('enable_math_captcha_for_contact_form', 0))
            <div class="field-set mb-4">
                <div class="contact-form-group">
                    <label class="contact-label required" for="math-group">{{ app('math-captcha')->label() }}</label>
                    {!! app('math-captcha')->input(['class' => 'contact-form-input', 'id' => 'math-group']) !!}
                </div>
            </div>
        @endif
    @endif

    {!! apply_filters('form_extra_fields_render', null) !!}

    {!! apply_filters('after_contact_form', null) !!}

    <div id='submit' class="mt20">
        <button type='submit' id='send_message' value='Book Consultation' class="ps-btn">
            Book Consultation
        </button>
    </div>

    <div class="contact-form-group mt-3">
        <div class="contact-message contact-success-message" style="display: none"></div>
        <div class="contact-message contact-error-message" style="display: none"></div>
    </div>

    {!! Form::close() !!}
