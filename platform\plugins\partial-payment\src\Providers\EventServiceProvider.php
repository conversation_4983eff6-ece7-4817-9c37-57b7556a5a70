<?php

namespace Shaqi\PartialPayment\Providers;

use Shaqi\PartialPayment\Events\PartialPaymentAdded;
use Shaqi\PartialPayment\Listeners\SendPartialPaymentNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        PartialPaymentAdded::class => [
            SendPartialPaymentNotification::class,
        ],
    ];
}