<x-core::card class="mb-3 mt-3 partial-shipments-card">
    <x-core::card.header>
        <h4 class="card-title">
            {{ trans('plugins/partial-shipment::partial-shipment.name') }}
        </h4>
    </x-core::card.header>
    <x-core::card.body>
        <p class="text-muted mb-3">
            {{ trans('plugins/partial-shipment::partial-shipment.description') }}
        </p>

        <x-core::button
            type="button"
            class="btn-create-partial-shipment w-100"
            icon="ti ti-plus"
        >
            {{ trans('plugins/partial-shipment::partial-shipment.create_partial_shipment') }}
        </x-core::button>
    </x-core::card.body>
</x-core::card>

<input type="hidden" id="order-id" value="{{ $order->id }}">

@push('footer')
<!-- Partial Shipment Assets -->
<link rel="stylesheet" href="{{ asset('vendor/core/plugins/partial-shipment/css/partial-shipment.css') }}">

<!-- Partial Shipment Modals -->
@include('plugins/partial-shipment::modals.create-partial-shipment')
@include('plugins/partial-shipment::modals.update-status')

<script src="{{ asset('vendor/core/plugins/partial-shipment/js/partial-shipment.js') }}"></script>

<script>
    // Add route helper for partial shipment routes
    if (typeof window.routes === 'undefined') {
        window.routes = {};
    }

    window.routes = {
        ...window.routes,
        'partial-shipments.order-items': '{{ route('partial-shipments.order-items') }}',
        'partial-shipments.store': '{{ route('partial-shipments.store') }}',
        'partial-shipments.update-status': '{{ route('partial-shipments.update-status', ':id') }}',
        'partial-shipments.destroy': '{{ route('partial-shipments.destroy', ':id') }}',
        'partial-shipments.by-order': '{{ route('partial-shipments.by-order', ':order_id') }}'
    };

    // Helper function to generate routes with parameters
    function route(name, params = {}) {
        let url = window.routes[name];
        if (!url) return '#';

        if (typeof params === 'string' || typeof params === 'number') {
            url = url.replace(':id', params).replace(':order_id', params);
        } else {
            Object.keys(params).forEach(key => {
                url = url.replace(`:${key}`, params[key]);
            });
        }

        return url;
    }

     window.orderData = {
        id: {{ $order->id }},
        code: '{{ $order->code }}',
        status: '{{ $order->status }}'
    };
</script>

@endpush

