<?php

namespace Shaqi\PartialShipment\Providers;

use Shaqi\Base\Supports\ServiceProvider;
use <PERSON>haqi\Base\Traits\LoadAndPublishDataTrait;
use Shaqi\PartialShipment\Services\PartialShipmentService;

class PartialShipmentServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->singleton(PartialShipmentService::class);
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/partial-shipment')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes(['web'])
            ->loadMigrations()
            ->publishAssets();

        $this->app->register(EventServiceProvider::class);
        $this->app->register(HookServiceProvider::class);
    }
}
