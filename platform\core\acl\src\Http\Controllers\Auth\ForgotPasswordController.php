<?php

namespace Shaqi\ACL\Http\Controllers\Auth;

use Shaqi\ACL\Forms\Auth\ForgotPasswordForm;
use Shaqi\ACL\Traits\SendsPasswordResetEmails;
use <PERSON><PERSON>qi\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;

class ForgotPasswordController extends BaseController
{
    use SendsPasswordResetEmails;

    public function __construct()
    {
        $this->middleware('guest');
    }

    public function showLinkRequestForm()
    {
        $this->pageTitle(trans('core/acl::auth.forgot_password.title'));

        return ForgotPasswordForm::create()->renderForm();
    }

    protected function sendResetLinkResponse(Request $request, $response)
    {
        return $this
            ->httpResponse()
            ->setMessage(trans($response))
            ->toResponse($request);
    }
}
