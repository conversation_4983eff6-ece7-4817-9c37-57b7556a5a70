<?php return array (
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'mews/purifier' => 
  array (
    'aliases' => 
    array (
      'Purifier' => 'Mews\\Purifier\\Facades\\Purifier',
    ),
    'providers' => 
    array (
      0 => 'Mews\\Purifier\\PurifierServiceProvider',
    ),
  ),
  'mollie/laravel-mollie' => 
  array (
    'aliases' => 
    array (
      'Mollie' => 'Mollie\\Laravel\\Facades\\Mollie',
    ),
    'providers' => 
    array (
      0 => 'Mollie\\Laravel\\MollieServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'shaqi/api' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Api\\Providers\\ApiServiceProvider',
    ),
    'aliases' => 
    array (
      'ApiHelper' => 'Shaqi\\Api\\Facades\\ApiHelper',
    ),
  ),
  'shaqi/assets' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Assets\\Providers\\AssetsServiceProvider',
    ),
    'aliases' => 
    array (
      'Assets' => 'Shaqi\\Assets\\Facades\\AssetsFacade',
    ),
  ),
  'shaqi/data-synchronize' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\DataSynchronize\\Providers\\DataSynchronizeServiceProvider',
    ),
  ),
  'shaqi/dev-tool' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\DevTool\\Providers\\DevToolServiceProvider',
    ),
  ),
  'shaqi/form-builder' => 
  array (
    'providers' => 
    array (
      0 => 'Kris\\LaravelFormBuilder\\FormBuilderServiceProvider',
    ),
    'aliases' => 
    array (
      'FormBuilder' => 'Kris\\LaravelFormBuilder\\Facades\\FormBuilder',
    ),
  ),
  'shaqi/get-started' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\GetStarted\\Providers\\GetStartedServiceProvider',
    ),
  ),
  'shaqi/git-commit-checker' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\GitCommitChecker\\Providers\\GitCommitCheckerServiceProvider',
    ),
  ),
  'shaqi/installer' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Installer\\Providers\\InstallerServiceProvider',
    ),
  ),
  'shaqi/menu' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Menu\\Providers\\MenuServiceProvider',
    ),
    'aliases' => 
    array (
      'Menu' => 'Shaqi\\Menu\\Facades\\Menu',
    ),
  ),
  'shaqi/optimize' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Optimize\\Providers\\OptimizeServiceProvider',
    ),
    'aliases' => 
    array (
      'OptimizerHelper' => 'Shaqi\\Optimize\\Facades\\OptimizerHelper',
    ),
  ),
  'shaqi/page' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Page\\Providers\\PageServiceProvider',
    ),
  ),
  'shaqi/platform' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Base\\Providers\\BaseServiceProvider',
      1 => 'Shaqi\\Base\\Providers\\CommandServiceProvider',
      2 => 'Shaqi\\Base\\Providers\\EventServiceProvider',
      3 => 'Shaqi\\Base\\Providers\\ComposerServiceProvider',
      4 => 'Shaqi\\Base\\Providers\\MailConfigServiceProvider',
      5 => 'Shaqi\\Base\\Providers\\FormServiceProvider',
      6 => 'Shaqi\\Support\\Providers\\SupportServiceProvider',
      7 => 'Shaqi\\Table\\Providers\\TableServiceProvider',
      8 => 'Shaqi\\ACL\\Providers\\AclServiceProvider',
      9 => 'Shaqi\\Dashboard\\Providers\\DashboardServiceProvider',
      10 => 'Shaqi\\Media\\Providers\\MediaServiceProvider',
      11 => 'Shaqi\\JsValidation\\Providers\\JsValidationServiceProvider',
      12 => 'Shaqi\\Chart\\Providers\\ChartServiceProvider',
      13 => 'Shaqi\\Icon\\Providers\\IconServiceProvider',
    ),
    'aliases' => 
    array (
      'Action' => 'Shaqi\\Base\\Facades\\Action',
      'AdminAppearance' => 'Shaqi\\Base\\Facades\\AdminAppearance',
      'AdminHelper' => 'Shaqi\\Base\\Facades\\AdminHelper',
      'Assets' => 'Shaqi\\Base\\Facades\\Assets',
      'BaseHelper' => 'Shaqi\\Base\\Facades\\BaseHelper',
      'Breadcrumbs' => 'Shaqi\\Base\\Facades\\Breadcrumbs',
      'DashboardMenu' => 'Shaqi\\Base\\Facades\\DashboardMenu',
      'CoreIcon' => 'Shaqi\\Icon\\Facades\\Icon',
      'EmailHandler' => 'Shaqi\\Base\\Facades\\EmailHandler',
      'Filter' => 'Shaqi\\Base\\Facades\\Filter',
      'Form' => 'Shaqi\\Base\\Facades\\Form',
      'Html' => 'Shaqi\\Base\\Facades\\Html',
      'JsValidator' => 'Shaqi\\JsValidation\\Facades\\JsValidator',
      'MacroableModels' => 'Shaqi\\Base\\Facades\\MacroableModels',
      'MetaBox' => 'Shaqi\\Base\\Facades\\MetaBox',
      'PageTitle' => 'Shaqi\\Base\\Facades\\PageTitle',
      'PanelSectionManager' => 'Shaqi\\Base\\Facades\\PanelSectionManager',
      'RvMedia' => 'Shaqi\\Media\\Facades\\RvMedia',
      'Setting' => 'Shaqi\\Setting\\Facades\\Setting',
    ),
  ),
  'shaqi/plugin-management' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\PluginManagement\\Providers\\PluginManagementServiceProvider',
    ),
  ),
  'shaqi/revision' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Revision\\Providers\\RevisionServiceProvider',
    ),
  ),
  'shaqi/seo-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\SeoHelper\\Providers\\SeoHelperServiceProvider',
    ),
    'aliases' => 
    array (
      'SeoHelper' => 'Shaqi\\SeoHelper\\Facades\\SeoHelper',
    ),
  ),
  'shaqi/shortcode' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Shortcode\\Providers\\ShortcodeServiceProvider',
    ),
    'aliases' => 
    array (
      'Shortcode' => 'Shaqi\\Shortcode\\Facades\\Shortcode',
    ),
  ),
  'shaqi/sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Sitemap\\Providers\\SitemapServiceProvider',
    ),
  ),
  'shaqi/slug' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Slug\\Providers\\SlugServiceProvider',
    ),
    'aliases' => 
    array (
      'SlugHelper' => 'Shaqi\\Slug\\Facades\\SlugHelper',
    ),
  ),
  'shaqi/theme' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Theme\\Providers\\ThemeServiceProvider',
      1 => 'Shaqi\\Theme\\Providers\\RouteServiceProvider',
    ),
    'aliases' => 
    array (
      'Theme' => 'Shaqi\\Theme\\Facades\\Theme',
      'ThemeOption' => 'Shaqi\\Theme\\Facades\\ThemeOption',
      'ThemeManager' => 'Shaqi\\Theme\\Facades\\Manager',
      'AdminBar' => 'Shaqi\\Theme\\Facades\\AdminBar',
      'SiteMapManager' => 'Shaqi\\Theme\\Facades\\SiteMapManager',
    ),
  ),
  'shaqi/widget' => 
  array (
    'providers' => 
    array (
      0 => 'Shaqi\\Widget\\Providers\\WidgetServiceProvider',
    ),
    'aliases' => 
    array (
      'Widget' => 'Shaqi\\Widget\\Facades\\Widget',
      'WidgetGroup' => 'Shaqi\\Widget\\Facades\\WidgetGroup',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'tightenco/ziggy' => 
  array (
    'providers' => 
    array (
      0 => 'Tighten\\Ziggy\\ZiggyServiceProvider',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-buttons' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\ButtonsServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-html' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\HtmlServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-oracle' => 
  array (
    'aliases' => 
    array (
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
    ),
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    ),
  ),
);