<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('ec_partial_shipments', function (Blueprint $table): void {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('shipment_id')->nullable();
            $table->string('partial_shipment_code')->unique();
            $table->text('note')->nullable();
            $table->string('status')->default('pending');
            $table->decimal('total_weight', 8, 2)->default(0);
            $table->decimal('shipping_cost', 15, 2)->default(0);
            $table->string('tracking_id')->nullable();
            $table->string('shipping_company_name')->nullable();
            $table->string('tracking_link')->nullable();
            $table->dateTime('estimate_date_shipped')->nullable();
            $table->dateTime('date_shipped')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('ec_orders')->onDelete('cascade');
            $table->foreign('shipment_id')->references('id')->on('ec_shipments')->onDelete('set null');
            $table->index(['order_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ec_partial_shipments');
    }
};
