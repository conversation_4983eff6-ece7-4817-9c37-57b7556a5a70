<?php

namespace Theme\TheBullionPeople\Http\Requests;

use Shaqi\Support\Http\Requests\Request;

class ConsultationRequest extends Request
{
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:120'],
            'last_name' => ['required', 'string', 'max:120'],
            'email' => ['required', 'string', 'email'],
            'phone' => ['required', 'string', 'max:120'],
            'best_time' => ['required', 'string', 'max:120'],
            'message' => ['nullable', 'string', 'max:1000'],
        ];
    }
    public function messages(): array
    {
        return [
            'first_name.required' => __('First name is required'),
            'last_name.required' => __('Last name is required'),
            'email.required' => __('Email is required'),
            'phone.required' => __('Phone is required'),
            'best_time.required' => __('Best time is required'),
        ];
    }
}

