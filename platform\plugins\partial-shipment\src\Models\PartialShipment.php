<?php

namespace S<PERSON>qi\PartialShipment\Models;

use <PERSON>haqi\Base\Models\BaseModel;
use <PERSON>haqi\Ecommerce\Models\Order;
use <PERSON>haqi\Ecommerce\Models\Shipment;
use Shaqi\PartialShipment\Enums\PartialShipmentStatusEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class PartialShipment extends BaseModel
{
    protected $table = 'ec_partial_shipments';

    protected $fillable = [
        'order_id',
        'shipment_id',
        'partial_shipment_code',
        'note',
        'status',
        'total_weight',
        'shipping_cost',
        'tracking_id',
        'shipping_company_name',
        'tracking_link',
        'estimate_date_shipped',
        'date_shipped',
        'created_by',
    ];

    protected $casts = [
        'status' => PartialShipmentStatusEnum::class,
        'total_weight' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'estimate_date_shipped' => 'datetime',
        'date_shipped' => 'datetime',
    ];

    protected static function booted(): void
    {
        static::creating(function (PartialShipment $partialShipment): void {
            if (empty($partialShipment->partial_shipment_code)) {
                $partialShipment->partial_shipment_code = static::generateUniqueCode();
            }
        });

        static::deleted(function (PartialShipment $partialShipment): void {
            $partialShipment->items()->delete();
        });
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id')->withDefault();
    }

    public function shipment(): BelongsTo
    {
        return $this->belongsTo(Shipment::class, 'shipment_id')->withDefault();
    }

    public function items(): HasMany
    {
        return $this->hasMany(PartialShipmentItem::class, 'partial_shipment_id');
    }

    public static function generateUniqueCode(): string
    {
        do {
            $code = 'PS-' . strtoupper(Str::random(8));
        } while (static::where('partial_shipment_code', $code)->exists());

        return $code;
    }

    public function getTotalItemsAttribute(): int
    {
        return $this->items()->sum('qty_shipped');
    }

    public function canBeEdited(): bool
    {
        return in_array($this->status->value, ['pending', 'processing']);
    }

    public function canBeCanceled(): bool
    {
        return in_array($this->status->value, ['pending', 'processing']);
    }
}
