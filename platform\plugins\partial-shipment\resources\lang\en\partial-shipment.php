<?php

return [
    'name' => 'Partial Shipment',
    'description' => 'Allow shipping orders partially by selecting specific products and quantities for each shipment',

    // General
    'create_partial_shipment' => 'Create Partial Shipment',
    'ship_partially' => 'Ship Partially',
    'create_shipment' => 'Create Shipment',
    'update_status' => 'Update Status',
    'cancel_shipment' => 'Cancel Shipment',
    'shipment_history' => 'Partial Shipment History',
    'no_partial_shipments' => 'No partial shipments found for this order.',

    // Form fields
    'note' => 'Note',
    'note_placeholder' => 'Add a note for this shipment...',
    'shipping_cost' => 'Shipping Cost',
    'tracking_id' => 'Tracking ID',
    'tracking_id_placeholder' => 'Enter tracking number',
    'shipping_company' => 'Shipping Company',
    'shipping_company_placeholder' => 'Enter shipping company name',
    'tracking_link' => 'Tracking Link',
    'tracking_link_placeholder' => 'Enter tracking URL',
    'estimate_date_shipped' => 'Estimated Shipping Date',
    'date_shipped' => 'Date Shipped',
    'status' => 'Status',
    'select_status' => 'Select Status',

    // Table headers
    'product' => 'Product',
    'ordered' => 'Ordered',
    'shipped' => 'Shipped',
    'remaining' => 'Remaining',
    'qty_to_ship' => 'Qty to Ship',
    'quantity' => 'Quantity',
    'weight' => 'Weight',
    'total_items' => 'Total Items',
    'total_weight' => 'Total Weight',
    'created_at' => 'Created At',
    'shipped_items' => 'Shipped Items',

    // Instructions and info
    'select_items_to_ship' => 'Select Items to Ship',
    'partial_shipment_info' => 'Select the products and quantities you want to ship in this partial shipment. The remaining quantities will be available for future shipments.',
    'status_update_info' => 'Update the status and tracking information for this partial shipment.',

    // Statuses
    'statuses' => [
        'pending' => 'Pending',
        'processing' => 'Processing',
        'shipped' => 'Shipped',
        'delivered' => 'Delivered',
        'canceled' => 'Canceled',
    ],

    // Modal titles
    'update_shipment_status' => 'Update Shipment Status',

    // Messages
    'created_successfully' => 'Partial shipment created successfully.',
    'updated_successfully' => 'Partial shipment updated successfully.',
    'status_updated_successfully' => 'Partial shipment status updated successfully.',
    'canceled_successfully' => 'Partial shipment canceled successfully.',
    'cannot_be_modified' => 'This partial shipment cannot be modified.',
    'cannot_be_canceled' => 'This partial shipment cannot be canceled.',
    'insufficient_quantity' => 'Insufficient quantity for product: :product',
    'order_cannot_have_partial_shipments' => 'This order cannot have partial shipments created.',

    // Validation messages
    'at_least_one_item_required' => 'At least one item must be selected for shipment.',
    'quantity_exceeds_remaining' => 'Quantity to ship (:qty) cannot exceed remaining quantity (:remaining) for :product.',
    'invalid_quantity' => 'Please enter a valid quantity.',
    'order_required' => 'Order is required.',
    'order_not_found' => 'Selected order does not exist.',

    // History messages
    'history' => [
        'created' => 'Partial shipment :code created with :items items',
        'status_updated' => 'Partial shipment :code status changed from :old_status to :new_status',
    ],

    // Permissions
    'permissions' => [
        'index' => 'View partial shipments',
        'create' => 'Create partial shipments',
        'edit' => 'Edit partial shipments',
        'destroy' => 'Delete partial shipments',
    ],
];
