<?php

namespace Shaqi\PartialPayment\Providers;

use Illuminate\Routing\Events\RouteMatched;
use <PERSON>haqi\Base\Facades\EmailHandler;
use <PERSON>haqi\Base\Traits\LoadAndPublishDataTrait;
use <PERSON><PERSON>qi\Base\Supports\ServiceProvider;

class PartialPaymentServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    // public function register(): void
    // {
    //     $this->app->register(EventServiceProvider::class);
    // }

    public function boot(): void
    {
        $this->setNamespace('plugins/partial-payment')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'email'])
            ->loadAndPublishViews()
            ->loadRoutes()
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->publishAssets();

        $this->app->register(HookServiceProvider::class);
        $this->app->register(EventServiceProvider::class);

         $this->app['events']->listen(RouteMatched::class, function () {
            EmailHandler::addTemplateSettings(PARTIAL_PAYMENT_MODULE_SCREEN_NAME, config('plugins.partial-payment.email', []));
        });
    }
}

