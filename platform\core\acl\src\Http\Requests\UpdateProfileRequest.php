<?php

namespace <PERSON>haqi\ACL\Http\Requests;

use <PERSON><PERSON>qi\Base\Rules\EmailRule;
use <PERSON><PERSON>qi\Support\Http\Requests\Request;

class UpdateProfileRequest extends Request
{
    public function rules(): array
    {
        return [
            'username' => ['required', 'string', 'alpha_dash', 'min:4', 'max:30'],
            'first_name' => ['required', 'string', 'max:60', 'min:2'],
            'last_name' => ['required', 'string', 'max:60', 'min:2'],
            'email' => ['required', 'max:60', 'min:6', new EmailRule()],
        ];
    }
}
