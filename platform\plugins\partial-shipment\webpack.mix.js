const mix = require('laravel-mix');

const path = require('path');
const directory = path.basename(path.resolve(__dirname));
const source = `platform/plugins/${directory}`;
const dist = `public/vendor/core/plugins/${directory}`;

mix
    .js(`${source}/resources/assets/js/partial-shipment.js`, `${dist}/js`)
    .sass(`${source}/resources/assets/sass/partial-shipment.scss`, `${dist}/css`)
    .copy(`${source}/resources/assets/css/partial-shipment.css`, `${dist}/css`)

if (mix.inProduction()) {
    mix.version();
}
