@php
   $page = Theme::get('page');
@endphp
{!! Theme::partial('header') !!}
<div class="ps-breadcrumb">
    <div class="ps-container">
        {!! Theme::partial('breadcrumbs') !!}
    </div>
</div>
<style>
    .ps-consultation-form {
    background: #ffffff;
    padding: 20px;
}
.ps-consultation{
    background-size: contain;
    position: relative;
}
.ps-consultation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.60);
}
.ps-consultation-content h1,
.ps-consultation-content h2,
.ps-consultation-content h3,
.ps-consultation-content h4,
.ps-consultation-content h5,
.ps-consultation-content h6,
.ps-consultation-content p,
.ps-consultation-content a{
    color: #ffffff;
}
</style>
<section class="ps-consultation" style="background-image: url({{ Theme::asset()->url('img/bullion-bg.jpeg') }})">
<div class="container">
    <div class="row justify-content-center align-items-center">
        <div class="col-lg-7 col-md-7 col-sm-12 col-xs-12">
           <div class="ps-consultation-content">
             {!! Theme::content() !!}
           </div>
        </div>
         <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12">
            <div class="ps-consultation-form">
                <h2 class="text-center">Book A Consultation</h2>
                {!! do_shortcode('[contact-form view="theme.thebullionpeople::partials.consultation-form"][/contact-form]') !!}
            </div>

        </div>
    </div>
</div>
</section>

{!! Theme::partial('footer') !!}
