<?php

namespace <PERSON><PERSON>qi\PartialShipment\Providers;

use <PERSON>haqi\Base\Supports\ServiceProvider;
use <PERSON>haqi\Base\Facades\Assets;
use <PERSON>haqi\Base\Facades\BaseHelper;
use <PERSON>haqi\Ecommerce\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Shaqi\Base\Facades\Html;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\Ecommerce\Enums\OrderStatusEnum;
use Shaqi\Payment\Enums\PaymentStatusEnum;
use Shaqi\Payment\Models\Payment;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {

        add_filter('ecommerce_order_detail_sidebar_bottom', function ($content, $order) {
            if (!$order || !Auth::check() || !Auth::user()->hasPermission('partial-shipments.create')) {
                return $content;
            }

            // Only show for orders that can have partial shipments
            if (!in_array($order->status, ['pending', 'processing'])) {
                return $content;
            }

            $partialShipmentButton = view('plugins/partial-shipment::partials.order-detail-button', compact('order'))->render();

            return $content . $partialShipmentButton;
        }, 10, 2);

        // Add partial shipment history to order detail page
        add_filter('ecommerce_order_detail_bottom', function ($content, $order) {
            if (!$order || !Auth::check() || !Auth::user()->hasPermission('partial-shipments.index')) {
                return $content;
            }

            $partialShipmentHistory = view('plugins/partial-shipment::partials.shipment-history', compact('order'))->render();

            return $content . $partialShipmentHistory;
        }, 10, 2);

        // Add JavaScript and CSS to order pages
        // add_filter('cms_settings_footer', function ($content) {
        //     if (request()->routeIs('orders.edit') || request()->routeIs('marketplace.vendor.orders.edit')) {
        //         $content .= view('plugins/partial-shipment::partials.assets')->render();
        //     }

        //     return $content;
        // });
    }


}
