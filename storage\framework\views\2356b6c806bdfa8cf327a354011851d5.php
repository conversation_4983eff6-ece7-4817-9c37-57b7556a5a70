<?php if (isset($component)) { $__componentOriginaldc8ac54b6bf7eb0d0560fdd5aa630687 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldc8ac54b6bf7eb0d0560fdd5aa630687 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'a74ad8dfacd4f985eb3977517615ce25::modal','data' => ['id' => 'update-partial-shipment-status-modal','title' => trans('plugins/partial-shipment::partial-shipment.update_shipment_status'),'buttonId' => 'confirm-update-partial-shipment-status-button','buttonLabel' => trans('plugins/partial-shipment::partial-shipment.update_status'),'size' => 'md']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'update-partial-shipment-status-modal','title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/partial-shipment::partial-shipment.update_shipment_status')),'button-id' => 'confirm-update-partial-shipment-status-button','button-label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/partial-shipment::partial-shipment.update_status')),'size' => 'md']); ?>
    <form id="update-partial-shipment-status-form">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        <input type="hidden" name="partial_shipment_id" id="update-partial-shipment-id">
        
        <div class="mb-3">
            <label class="form-label"><?php echo e(trans('plugins/partial-shipment::partial-shipment.status')); ?> <span class="text-danger">*</span></label>
            <select name="status" class="form-select" required>
                <option value=""><?php echo e(trans('plugins/partial-shipment::partial-shipment.select_status')); ?></option>
                <option value="pending"><?php echo e(trans('plugins/partial-shipment::partial-shipment.statuses.pending')); ?></option>
                <option value="processing"><?php echo e(trans('plugins/partial-shipment::partial-shipment.statuses.processing')); ?></option>
                <option value="shipped"><?php echo e(trans('plugins/partial-shipment::partial-shipment.statuses.shipped')); ?></option>
                <option value="delivered"><?php echo e(trans('plugins/partial-shipment::partial-shipment.statuses.delivered')); ?></option>
                <option value="canceled"><?php echo e(trans('plugins/partial-shipment::partial-shipment.statuses.canceled')); ?></option>
            </select>
        </div>

        <div class="mb-3">
            <label class="form-label"><?php echo e(trans('plugins/partial-shipment::partial-shipment.tracking_id')); ?></label>
            <input type="text" name="tracking_id" class="form-control" placeholder="<?php echo e(trans('plugins/partial-shipment::partial-shipment.tracking_id_placeholder')); ?>">
        </div>

        <div class="mb-3">
            <label class="form-label"><?php echo e(trans('plugins/partial-shipment::partial-shipment.shipping_company')); ?></label>
            <input type="text" name="shipping_company_name" class="form-control" placeholder="<?php echo e(trans('plugins/partial-shipment::partial-shipment.shipping_company_placeholder')); ?>">
        </div>

        <div class="mb-3">
            <label class="form-label"><?php echo e(trans('plugins/partial-shipment::partial-shipment.tracking_link')); ?></label>
            <input type="url" name="tracking_link" class="form-control" placeholder="<?php echo e(trans('plugins/partial-shipment::partial-shipment.tracking_link_placeholder')); ?>">
        </div>

        <div class="mb-3">
            <label class="form-label"><?php echo e(trans('plugins/partial-shipment::partial-shipment.date_shipped')); ?></label>
            <input type="datetime-local" name="date_shipped" class="form-control">
        </div>

        <div class="alert alert-info">
            <i class="ti ti-info-circle me-2"></i>
            <?php echo e(trans('plugins/partial-shipment::partial-shipment.status_update_info')); ?>

        </div>
    </form>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldc8ac54b6bf7eb0d0560fdd5aa630687)): ?>
<?php $attributes = $__attributesOriginaldc8ac54b6bf7eb0d0560fdd5aa630687; ?>
<?php unset($__attributesOriginaldc8ac54b6bf7eb0d0560fdd5aa630687); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldc8ac54b6bf7eb0d0560fdd5aa630687)): ?>
<?php $component = $__componentOriginaldc8ac54b6bf7eb0d0560fdd5aa630687; ?>
<?php unset($__componentOriginaldc8ac54b6bf7eb0d0560fdd5aa630687); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\thebullionpeople\platform/plugins/partial-shipment/resources/views/modals/update-status.blade.php ENDPATH**/ ?>