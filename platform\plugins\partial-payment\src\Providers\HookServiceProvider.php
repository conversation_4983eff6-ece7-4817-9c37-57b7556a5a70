<?php

namespace <PERSON><PERSON>qi\PartialPayment\Providers;

use Shaqi\Base\Supports\ServiceProvider;
use Shaqi\Base\Facades\Assets;
use <PERSON>haqi\Base\Facades\BaseHelper;
use <PERSON><PERSON>qi\Ecommerce\Models\Order;
use Illuminate\Http\Request;
use <PERSON>haqi\Base\Facades\Html;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\Ecommerce\Enums\OrderStatusEnum;
use Shaqi\Payment\Enums\PaymentStatusEnum;
use Shaqi\Payment\Models\Payment;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        // Add partial payment button to order detail page
        add_filter('ecommerce_order_detail_extra_payment_buttons', [$this, 'registerPartialPaymentButton'], 10, 2);

    }

    public function registerPartialPaymentButton(?string $html, Order $order): string
    {
         Assets::addScriptsDirectly('vendor/core/plugins/partial-payment/js/partial-payment.js');

        if ($order->payment->status != PaymentStatusEnum::COMPLETED) {

            $paidAmount = Payment::query()
                ->where('order_id', $order->id)
                ->where('status', PaymentStatusEnum::COMPLETED)->whereNotIn('id', [$order->payment->id])
                ->sum('amount');

            return $html . view('plugins/partial-payment::partial-payment-button', compact('order', 'paidAmount'))->render();
        }

        return $html;
    }
}
