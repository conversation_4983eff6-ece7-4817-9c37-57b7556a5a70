# Partial Payment Plugin for Shaqi CMS

This plugin allows administrators to add partial payments to orders in Shaqi CMS.

## Overview

The Partial Payment plugin enables customers to make multiple payments for a single order, allowing for flexible payment arrangements and installment options.

## Requirements

- Shaqi CMS 7.5.1 or higher
- Shaqi Ecommerce plugin
- Shaqi Payment plugin

## Installation

1. Download the plugin from the repository.
2. Extract the downloaded file and upload the extracted folder to the `platform/plugins` directory.
3. Go to **Admin** > **Plugins** and click on the **Activate** button for the Partial Payment plugin.

## Features

- Add partial payments to existing orders
- Track remaining balance on orders
- Support for multiple payment methods
- Detailed payment history

## Integration Changes

The following changes were made to integrate with other plugins:

### Ecommerce Plugin Integration

1. Added a partial payment button to the order detail page using the filter hook:
   ```php
   add_filter('ecommerce_order_detail_extra_payment_buttons', [$this, 'registerPartialPaymentButton'], 10, 2);
   ```

2. The button appears on orders with pending payments in `platform/plugins/ecommerce/resources/views/orders/edit.blade.php`

3. Added a new action to `OrderHistoryActionEnum` to track partial payment history:
   ```php
   public const CONFIRM_PARTIAL_PAYMENT = 'confirm_partial_payment';
   ```

4. Creates order history entries when partial payments are made

### Payment Plugin Integration

1. Utilizes the Payment model and PaymentStatusEnum from the Payment plugin
2. Extends payment functionality to handle partial payments
3. Adds a new payment type 'partial' to distinguish partial payments from regular payments
4. Updates the original payment record when partial payments are made:
   - Updates the amount field to reflect total paid amount
   - Updates status to COMPLETED when full payment is received

## Technical Implementation Details

### Database Changes

No database schema changes were required for the core plugins. The partial payment plugin leverages the existing Payment model with the following approach:

1. The original payment record for an order remains with status PENDING until fully paid
2. Additional payment records are created with:
   - `payment_type` set to 'partial'
   - `status` set to COMPLETED
   - `charge_id` prefixed with 'partial-' followed by timestamp
   - `amount` set to the partial payment amount

### JavaScript Integration

The plugin adds a custom JavaScript file that:
1. Handles the partial payment modal interaction
2. Manages AJAX submission of partial payments
3. Refreshes the order page after successful payment

## Usage

1. Navigate to an order detail page in the admin panel
2. Click on the "Add Partial Payment" button
3. Enter the payment amount (must be less than or equal to the remaining balance)
4. Select the payment method
5. Add an optional description
6. Submit the payment

## Handling Future Updates

When the Ecommerce or Payment plugins are updated in the future, the following areas may need attention to maintain compatibility with the Partial Payment plugin:

### Ecommerce Plugin Updates

1. **Order Edit View**: If the order edit view (`platform/plugins/ecommerce/resources/views/orders/edit.blade.php`) is modified, ensure the filter hook `ecommerce_order_detail_extra_payment_buttons` is still present and working correctly.

2. **Order History Actions**: If the `OrderHistoryActionEnum` class is updated, ensure the `CONFIRM_PARTIAL_PAYMENT` constant is preserved or re-added if necessary.

3. **Order Model**: If the Order model or its relationships are modified, verify that the relationship with Payment model still works correctly.

### Payment Plugin Updates

1. **Payment Model**: If the Payment model schema changes, ensure the fields used by partial payments (especially `payment_type`, `status`, and `amount`) are still available and function as expected.

2. **Payment Status Enum**: If the `PaymentStatusEnum` is modified, ensure the statuses used by partial payments (especially `COMPLETED` and `PENDING`) are still available.

3. **Payment Processing Logic**: If the payment processing logic in the Payment plugin changes, verify that partial payments are still processed correctly.

### Testing After Updates

After updating either plugin, test the partial payment functionality by:

1. Creating a new order
2. Adding a partial payment to the order
3. Verifying the payment is recorded correctly
4. Checking that the order status and payment status are updated appropriately
5. Adding additional partial payments to complete the order
6. Verifying the order is marked as fully paid when the total amount is reached

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
