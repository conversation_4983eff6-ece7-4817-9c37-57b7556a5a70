<?php

namespace <PERSON>haqi\ACL\Http\Requests;

use <PERSON><PERSON>qi\Base\Rules\EmailRule;
use <PERSON>haqi\Support\Http\Requests\Request;

class ResetPasswordRequest extends Request
{
    public function rules(): array
    {
        return [
            'token' => ['required', 'string'],
            'email' => ['required', new EmailRule()],
            'password' => ['required', 'confirmed', 'min:6'],
        ];
    }
}
