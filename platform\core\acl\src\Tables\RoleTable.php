<?php

namespace <PERSON><PERSON>qi\ACL\Tables;

use <PERSON>haqi\ACL\Models\Role;
use Shaqi\Base\Facades\BaseHelper;
use Shaqi\Table\Abstracts\TableAbstract;
use Shaqi\Table\Actions\DeleteAction;
use <PERSON>haqi\Table\Actions\EditAction;
use Shaqi\Table\BulkActions\DeleteBulkAction;
use Shaqi\Table\BulkChanges\NameBulkChange;
use Shaqi\Table\Columns\CreatedAtColumn;
use Shaqi\Table\Columns\FormattedColumn;
use Shaqi\Table\Columns\IdColumn;
use Shaqi\Table\Columns\LinkableColumn;
use Shaqi\Table\Columns\NameColumn;
use Shaqi\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class RoleTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Role::class)
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('roles.edit'),
                FormattedColumn::make('description')
                    ->title(trans('core/base::tables.description'))
                    ->alignStart()
                    ->withEmptyState(),
                CreatedAtColumn::make(),
                LinkableColumn::make('created_by')
                    ->urlUsing(fn (LinkableColumn $column) => $column->getItem()->author->url)
                    ->title(trans('core/acl::permissions.created_by'))
                    ->width(100)
                    ->getValueUsing(function (LinkableColumn $column) {
                        return BaseHelper::clean($column->getItem()->author->name);
                    })
                    ->externalLink()
                    ->withEmptyState(),
            ])
            ->addHeaderAction(CreateHeaderAction::make()->route('roles.create'))
            ->addActions([
                EditAction::make()->route('roles.edit'),
                DeleteAction::make()->route('roles.destroy'),
            ])
            ->addBulkAction(DeleteBulkAction::make()->permission('roles.destroy'))
            ->addBulkChange(NameBulkChange::make())
            ->queryUsing(function (Builder $query): void {
                $query
                    ->with('author')
                    ->select([
                        'id',
                        'name',
                        'description',
                        'created_at',
                        'created_by',
                    ]);
            });
    }
}
