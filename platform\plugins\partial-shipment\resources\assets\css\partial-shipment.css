/* Partial Shipment Styles */

.partial-shipments-history {
    margin-top: 1rem;
}

.partial-shipment-item {
    border-left: 4px solid #007bff;
}

.partial-shipment-item .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.shipment-actions .btn {
    margin-left: 0.25rem;
}

.qty-to-ship-input:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.item-checkbox:checked + .qty-to-ship-input {
    background-color: #fff;
    opacity: 1;
}

.partial-shipment-item[data-status="pending"] {
    border-left-color: #ffc107;
}

.partial-shipment-item[data-status="processing"] {
    border-left-color: #17a2b8;
}

.partial-shipment-item[data-status="shipped"] {
    border-left-color: #007bff;
}

.partial-shipment-item[data-status="delivered"] {
    border-left-color: #28a745;
}

.partial-shipment-item[data-status="canceled"] {
    border-left-color: #dc3545;
}

.shipment-details p,
.shipment-summary p {
    margin-bottom: 0.5rem;
}

.tracking-link {
    text-decoration: none;
}

.tracking-link:hover {
    text-decoration: underline;
}

#partial-shipment-items-table .item-image {
    border-radius: 4px;
    object-fit: cover;
}

.shipped-items-list .item-image {
    border-radius: 4px;
    object-fit: cover;
}

/* Modal specific styles */
#create-partial-shipment-modal .modal-dialog {
    max-width: 90%;
}

#create-partial-shipment-modal .table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .shipment-actions {
        margin-top: 0.5rem;
    }
    
    .shipment-actions .btn {
        margin-bottom: 0.25rem;
    }
    
    #create-partial-shipment-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
}

/* Loading states */
.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Status badges */
.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
