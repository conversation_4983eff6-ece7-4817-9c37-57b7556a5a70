# Partial Shipment Plugin for Shaqi CMS

This plugin allows administrators to ship orders partially by selecting specific products and quantities for each shipment, providing better flexibility in order fulfillment.

## Features

- **Partial Order Fulfillment**: Ship orders in multiple batches by selecting specific products and quantities
- **Shipment Tracking**: Track each partial shipment with individual tracking IDs and shipping companies
- **Order Status Management**: Automatically update order status based on shipping completion
- **Shipment History**: View complete history of all partial shipments for each order
- **Quantity Tracking**: Track shipped and remaining quantities for each order item
- **Admin Interface**: Easy-to-use modal interface for creating and managing partial shipments
- **Permissions**: Granular permissions for managing partial shipments
- **Order History Integration**: All partial shipment activities are logged in order history

## Installation

1. Copy the plugin to `platform/plugins/partial-shipment/`
2. Run migrations: `php artisan migrate`
3. Activate the plugin in the admin panel
4. Assign permissions to users who need to manage partial shipments

## Usage

### Creating Partial Shipments

1. Go to an order detail page
2. Click the "Ship Partially" button in the sidebar
3. Select the products and quantities you want to ship
4. Add shipping information (optional):
   - Tracking ID
   - Shipping company
   - Tracking link
   - Estimated shipping date
   - Shipping cost
   - Notes
5. Click "Create Shipment"

### Managing Partial Shipments

- View all partial shipments in the order history section
- Update shipment status and tracking information
- Cancel partial shipments (if not yet shipped)
- Track remaining quantities for each product

### Order Status Updates

The plugin automatically updates order status based on shipping progress:
- **Pending** → **Processing**: When first partial shipment is created
- **Processing** → **Completed**: When all items have been shipped

## Database Schema

### Partial Shipments Table (`ec_partial_shipments`)
- Stores main shipment information
- Links to orders and optionally to main shipments
- Tracks status, costs, and shipping details

### Partial Shipment Items Table (`ec_partial_shipment_items`)
- Stores individual items in each partial shipment
- Links to order products and products
- Tracks quantities and item-specific details

### Order Products Updates
- Added `qty_shipped` and `qty_remaining` columns
- Automatically updated when partial shipments are created/canceled

## Permissions

- `partial-shipments.index`: View partial shipments
- `partial-shipments.create`: Create partial shipments
- `partial-shipments.edit`: Edit partial shipments
- `partial-shipments.destroy`: Delete partial shipments

## API Endpoints

- `GET /partial-shipments/order-items`: Get order items for partial shipment
- `POST /partial-shipments`: Create new partial shipment
- `PUT /partial-shipments/{id}`: Update partial shipment
- `PUT /partial-shipments/{id}/status`: Update shipment status
- `DELETE /partial-shipments/{id}`: Cancel partial shipment
- `GET /partial-shipments/order/{order_id}`: Get partial shipments for order

## Events

- `PartialShipmentCreated`: Fired when a new partial shipment is created
- `PartialShipmentStatusUpdated`: Fired when shipment status is updated

## Requirements

- Shaqi CMS 7.3.0 or higher
- Ecommerce plugin must be installed and activated

## Support

For support and bug reports, please contact Shaqi Technologies.

## License

This plugin is proprietary software developed by Shaqi Technologies.
