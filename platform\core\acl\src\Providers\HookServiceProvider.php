<?php

namespace <PERSON>haqi\ACL\Providers;

use <PERSON><PERSON><PERSON>\ACL\Hooks\UserWidgetHook;
use <PERSON>haqi\Base\Supports\ServiceProvider;
use <PERSON>haqi\Dashboard\Events\RenderingDashboardWidgets;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->app['events']->listen(RenderingDashboardWidgets::class, function (): void {
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [UserWidgetHook::class, 'addUserStatsWidget'], 12, 2);
        });
    }
}
