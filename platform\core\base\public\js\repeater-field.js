$((function(){var t,e=(t={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},function(e){return null==t?void 0:t[e]}),a=/&(?:amp|lt|gt|quot|#39);/g,n=RegExp(a.source);$(document).on("click",'[data-target="repeater-add"]',(function(){var t,i=$(this).data("id"),o=$("#".concat(i,"_group")),d=$("#".concat(i,"_template")),r=$("#".concat(i,"_fields")),c=parseInt(o.data("nextIndex")),_=d.html(),g=r.text();_=_.replace(/__key__/g,c),g=g.replace(/__key__/g,c),_=_.replace(/__fields__/g,(t=(t=g).toString())&&n.test(t)?t.replace(a,e):t),o.append(_),o.data("nextIndex",c+1),window.Shaqi&&(window.Shaqi.initResources(),window.Shaqi.initMediaIntegrate(),window.Shaqi.initCoreIcon()),window.EditorManagement&&(window.EDITOR=(new EditorManagement).init())})),$(document).on("click",'[data-target="repeater-remove"]',(function(){var t=$(this).data("id");$('[data-id="'.concat(t,'"]')).remove()}))}));
//# sourceMappingURL=repeater-field.js.map