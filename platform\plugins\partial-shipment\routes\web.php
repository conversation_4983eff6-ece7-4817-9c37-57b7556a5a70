<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Shaqi\PartialShipment\Http\Controllers\PartialShipmentController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function (): void {
    Route::group([
        'namespace' => 'Shaqi\PartialShipment\Http\Controllers',
        'prefix' => 'partial-shipments',
        'middleware' => 'web',
    ], function (): void {
        Route::get('order-items', [PartialShipmentController::class, 'getOrderItems'])
            ->name('partial-shipments.order-items')
            ->permission('partial-shipments.index');

        Route::post('/', [PartialShipmentController::class, 'store'])
            ->name('partial-shipments.store')
            ->permission('partial-shipments.create');

        Route::put('{partial_shipment}', [PartialShipmentController::class, 'update'])
            ->name('partial-shipments.update')
            ->permission('partial-shipments.edit');

        Route::put('{partial_shipment}/status', [PartialShipmentController::class, 'updateStatus'])
            ->name('partial-shipments.update-status')
            ->permission('partial-shipments.edit');

        Route::delete('{partial_shipment}', [PartialShipmentController::class, 'destroy'])
            ->name('partial-shipments.destroy')
            ->permission('partial-shipments.destroy');

        Route::get('order/{order_id}', [PartialShipmentController::class, 'getPartialShipments'])
            ->name('partial-shipments.by-order')
            ->permission('partial-shipments.index');
    });
});
