<!-- Partial Shipment Assets -->
<link rel="stylesheet" href="{{ asset('vendor/core/plugins/partial-shipment/css/partial-shipment.css') }}">

<!-- Partial Shipment Modals -->
@include('plugins/partial-shipment::modals.create-partial-shipment')
@include('plugins/partial-shipment::modals.update-status')

<script src="{{ asset('vendor/core/plugins/partial-shipment/js/partial-shipment.js') }}"></script>

<script>
    // Add route helper for partial shipment routes
    if (typeof window.routes === 'undefined') {
        window.routes = {};
    }
    
    window.routes = {
        ...window.routes,
        'partial-shipments.order-items': '{{ route('partial-shipments.order-items') }}',
        'partial-shipments.store': '{{ route('partial-shipments.store') }}',
        'partial-shipments.update-status': '{{ route('partial-shipments.update-status', ':id') }}',
        'partial-shipments.destroy': '{{ route('partial-shipments.destroy', ':id') }}',
        'partial-shipments.by-order': '{{ route('partial-shipments.by-order', ':order_id') }}'
    };
    
    // Helper function to generate routes with parameters
    function route(name, params = {}) {
        let url = window.routes[name];
        if (!url) return '#';
        
        if (typeof params === 'string' || typeof params === 'number') {
            url = url.replace(':id', params).replace(':order_id', params);
        } else {
            Object.keys(params).forEach(key => {
                url = url.replace(`:${key}`, params[key]);
            });
        }
        
        return url;
    }
</script>
