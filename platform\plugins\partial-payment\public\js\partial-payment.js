'use strict';

$(document).ready(function () {
    // Open modal when clicking the partial payment button
    $(document).on('click', '.btn-trigger-partial-payment', function (e) {
        e.preventDefault();

        const paymentId = $(this).data('payment-id');
        const orderId = $(this).data('order-id');
        const remaining = $(this).data('remaining');
        const paymentMethod = $(this).data('payment-method');

        $('#partial-payment-payment-id').val(paymentId);
        $('#partial-payment-order-id').val(orderId);
        $('#partial-payment-amount').attr('max', remaining);
        $('#remaining-amount').text(remaining);
        $('#partial-payment-method').val(paymentMethod);

        $('#partial-payment-modal').modal('show');
    });

    // Handle form submission
    $(document).on('click', '#partial-payment-submit', function (e) {
        e.preventDefault();

        const $button = $(this);
        $button.addClass('button-loading');

        $.ajax({
            type: 'POST',
            cache: false,
            url: route('partial-payments.store'),
            data: $('#partial-payment-form').serialize(),
            success: res => {
                if (!res.error) {
                    Shaqi.showSuccess(res.message);
                    $('#partial-payment-modal').modal('hide');

                    if (res.data && res.data.next_url) {
                        window.location.href = res.data.next_url;
                    }
                } else {
                    Shaqi.showError(res.message);
                }

                $button.removeClass('button-loading');
            },
            error: res => {
                Shaqi.handleError(res);
                $button.removeClass('button-loading');
            }
        });
    });
});
