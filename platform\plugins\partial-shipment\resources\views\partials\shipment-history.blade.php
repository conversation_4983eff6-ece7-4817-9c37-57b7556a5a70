<div class="partial-shipments-history" id="partial-shipments-history">
    <h6 class="mb-3">
        <i class="ti ti-truck me-2"></i>
        {{ trans('plugins/partial-shipment::partial-shipment.shipment_history') }}
    </h6>
    
    <div id="partial-shipments-list">
        <!-- Partial shipments will be loaded here via JavaScript -->
    </div>
    
    <div id="no-partial-shipments" class="text-center text-muted py-3" style="display: none;">
        <i class="ti ti-package-off fs-1 mb-2"></i>
        <p>{{ trans('plugins/partial-shipment::partial-shipment.no_partial_shipments') }}</p>
    </div>
</div>

<!-- Template for partial shipment item -->
<template id="partial-shipment-template">
    <div class="card mb-3 partial-shipment-item">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <strong class="shipment-code"></strong>
                <span class="shipment-status ms-2"></span>
            </div>
            <div class="shipment-actions">
                <button type="button" class="btn btn-sm btn-outline-primary btn-update-status" data-bs-toggle="tooltip" title="{{ trans('plugins/partial-shipment::partial-shipment.update_status') }}">
                    <i class="ti ti-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger btn-cancel-shipment" data-bs-toggle="tooltip" title="{{ trans('plugins/partial-shipment::partial-shipment.cancel_shipment') }}">
                    <i class="ti ti-x"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="shipment-details">
                        <p class="mb-1"><strong>{{ trans('plugins/partial-shipment::partial-shipment.created_at') }}:</strong> <span class="created-at"></span></p>
                        <p class="mb-1 tracking-info" style="display: none;">
                            <strong>{{ trans('plugins/partial-shipment::partial-shipment.tracking_id') }}:</strong> 
                            <span class="tracking-id"></span>
                            <a href="#" class="tracking-link ms-2" target="_blank" style="display: none;">
                                <i class="ti ti-external-link"></i>
                            </a>
                        </p>
                        <p class="mb-1 shipping-company" style="display: none;">
                            <strong>{{ trans('plugins/partial-shipment::partial-shipment.shipping_company') }}:</strong> 
                            <span class="company-name"></span>
                        </p>
                        <p class="mb-1 date-shipped" style="display: none;">
                            <strong>{{ trans('plugins/partial-shipment::partial-shipment.date_shipped') }}:</strong> 
                            <span class="shipped-date"></span>
                        </p>
                        <p class="mb-0 shipment-note" style="display: none;">
                            <strong>{{ trans('plugins/partial-shipment::partial-shipment.note') }}:</strong> 
                            <span class="note-text"></span>
                        </p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="shipment-summary">
                        <p class="mb-1"><strong>{{ trans('plugins/partial-shipment::partial-shipment.total_items') }}:</strong> <span class="total-items"></span></p>
                        <p class="mb-1"><strong>{{ trans('plugins/partial-shipment::partial-shipment.total_weight') }}:</strong> <span class="total-weight"></span></p>
                        <p class="mb-0 shipping-cost" style="display: none;">
                            <strong>{{ trans('plugins/partial-shipment::partial-shipment.shipping_cost') }}:</strong> 
                            <span class="cost-amount"></span>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <h6>{{ trans('plugins/partial-shipment::partial-shipment.shipped_items') }}:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{{ trans('plugins/partial-shipment::partial-shipment.product') }}</th>
                                <th width="100">{{ trans('plugins/partial-shipment::partial-shipment.quantity') }}</th>
                                <th width="100">{{ trans('plugins/partial-shipment::partial-shipment.weight') }}</th>
                            </tr>
                        </thead>
                        <tbody class="shipped-items-list">
                            <!-- Items will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Template for shipped item row -->
<template id="shipped-item-template">
    <tr>
        <td>
            <div class="d-flex align-items-center">
                <img class="item-image me-2" src="" alt="" width="40" height="40" style="object-fit: cover; border-radius: 4px;">
                <div>
                    <div class="item-name fw-medium"></div>
                    <small class="text-muted item-options"></small>
                </div>
            </div>
        </td>
        <td class="item-quantity"></td>
        <td class="item-weight"></td>
    </tr>
</template>
