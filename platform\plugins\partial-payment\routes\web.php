<?php

use Shaqi\Base\Facades\AdminHelper;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function (): void {
    Route::group(['namespace' => 'Shaqi\PartialPayment\Http\Controllers', 'middleware' => ['web', 'core']], function (): void {
        Route::group(['prefix' => 'admin', 'middleware' => 'auth'], function (): void {
            Route::group(['prefix' => 'partial-payments', 'as' => 'partial-payments.'], function (): void {
                Route::post('store', [
                    'as' => 'store',
                    'uses' => 'PartialPaymentController@store',
                    'permission' => 'orders.edit',
                ]);
            });
        });
    });
});