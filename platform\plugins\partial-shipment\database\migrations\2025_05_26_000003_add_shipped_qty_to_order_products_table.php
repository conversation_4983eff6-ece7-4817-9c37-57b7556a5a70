<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('ec_order_product', function (Blueprint $table): void {
            if (!Schema::hasColumn('ec_order_product', 'qty_shipped')) {
                $table->integer('qty_shipped')->default(0)->after('qty');
            }
            if (!Schema::hasColumn('ec_order_product', 'qty_remaining')) {
                $table->integer('qty_remaining')->default(0)->after('qty_shipped');
            }
        });

        // Update existing records to set qty_remaining = qty for existing orders
        DB::statement('UPDATE ec_order_product SET qty_remaining = qty WHERE qty_remaining = 0');
    }

    public function down(): void
    {
        Schema::table('ec_order_product', function (Blueprint $table): void {
            $table->dropColumn(['qty_shipped', 'qty_remaining']);
        });
    }
};
