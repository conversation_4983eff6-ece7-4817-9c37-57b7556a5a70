(()=>{"use strict";$((function(){$(document).on("submit",'[data-bb-toggle="activate-license"]',(function(t){t.preventDefault();var a=$(this),e=new FormData(t.currentTarget);Shaqi.showLoading(a[0]),$httpClient.make().postForm(a.prop("action"),e).then((function(t){var e=t.data;if(Shaqi.showSuccess(e.message),a.data("reload"))setTimeout((function(){window.location.reload()}),1e3);else{var i=a.data("redirect");i&&window.location.assign(i)}})).finally((function(){Shaqi.hideLoading(a[0])}))}))}))})();
//# sourceMappingURL=license-activation.js.map