<?php

namespace Database\Seeders;

use <PERSON><PERSON><PERSON>\ACL\Database\Seeders\UserSeeder;
use Shaqi\Base\Supports\BaseSeeder;
use <PERSON>haqi\Ecommerce\Database\Seeders\CurrencySeeder;
use <PERSON>haqi\Ecommerce\Database\Seeders\ProductSpecificationSeeder;
use <PERSON><PERSON><PERSON>\Ecommerce\Database\Seeders\ReviewSeeder;
use Shaqi\Ecommerce\Database\Seeders\ShippingSeeder;
use Shaqi\Ecommerce\Database\Seeders\TaxSeeder;
use Shaqi\Language\Database\Seeders\LanguageSeeder;

class DatabaseSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->prepareRun();

        $this->call([
            LanguageSeeder::class,
            BrandSeeder::class,
            CurrencySeeder::class,
            ProductCategorySeeder::class,
            ProductCollectionSeeder::class,
            ProductLabelSeeder::class,
            ProductSeeder::class,
            ProductAttributeSeeder::class,
            CustomerSeeder::class,
            ReviewSeeder::class,
            TaxSeeder::class,
            ProductTagSeeder::class,
            FlashSaleSeeder::class,
            ShippingSeeder::class,
            ContactSeeder::class,
            UserSeeder::class,
            BlogSeeder::class,
            SimpleSliderSeeder::class,
            PageSeeder::class,
            AdsSeeder::class,
            FaqSeeder::class,
            SettingSeeder::class,
            ProductSpecificationSeeder::class,
            StoreLocatorSeeder::class,
            MenuSeeder::class,
            ThemeOptionSeeder::class,
            WidgetSeeder::class,
            ProductOptionSeeder::class,
            MarketplaceSeeder::class,
        ]);

        $this->finished();
    }
}
