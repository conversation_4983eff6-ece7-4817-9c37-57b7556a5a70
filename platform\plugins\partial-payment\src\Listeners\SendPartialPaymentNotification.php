<?php

namespace Shaqi\PartialPayment\Listeners;

use <PERSON><PERSON>qi\Base\Facades\EmailHandler;
use <PERSON>haqi\PartialPayment\Events\PartialPaymentAdded;
use Shaqi\Payment\Models\Payment;

class SendPartialPaymentNotification
{
    public function handle(PartialPaymentAdded $event): void
    {
        $order = $event->order;
        $payment = $event->payment;

        $paidAmount = Payment::query()
            ->where('order_id', $order->id)
            ->where('status', 'completed')
            ->whereNotIn('id', [$order->payment->id])
            ->sum('amount');

        $remainingAmount = $order->amount - $paidAmount;

        EmailHandler::setModule(PARTIAL_PAYMENT_MODULE_SCREEN_NAME)
            ->setVariableValues([
                'customer_name' => $order->user->name ?: $order->address->name,
                'order_code' => $order->code,
                'order_link' => route('public.orders.tracking', ['order_id' => $order->code, 'email' => $order->user->email ?: $order->address->email]),
                'payment_amount' => format_price($payment->amount),
                'remaining_amount' => format_price($remainingAmount),
            ])
            ->sendUsingTemplate(
                'partial-payment-added',
                $order->user->email ?: $order->address->email
            );
    }
}
