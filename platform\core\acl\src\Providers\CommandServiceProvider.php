<?php

namespace <PERSON><PERSON>qi\ACL\Providers;

use <PERSON><PERSON><PERSON>\ACL\Commands\UserCreateCommand;
use Shaqi\ACL\Commands\UserPasswordCommand;
use Shaqi\Base\Supports\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            UserCreateCommand::class,
            UserPasswordCommand::class,
        ]);
    }
}
