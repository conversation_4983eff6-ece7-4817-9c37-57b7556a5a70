<?php

namespace Shaqi\PartialShipment\Listeners;

use <PERSON><PERSON>qi\Ecommerce\Enums\OrderHistoryActionEnum;
use <PERSON><PERSON>qi\Ecommerce\Models\OrderHistory;
use <PERSON><PERSON>qi\PartialShipment\Events\PartialShipmentCreated;
use <PERSON><PERSON>qi\PartialShipment\Events\PartialShipmentStatusUpdated;
use Illuminate\Support\Facades\Auth;

class PartialShipmentEventListener
{
    public function handlePartialShipmentCreated(PartialShipmentCreated $event): void
    {
        $partialShipment = $event->partialShipment;

        // Create order history entry
        OrderHistory::create([
            'action' => OrderHistoryActionEnum::CREATE_SHIPMENT,
            'description' => trans('plugins/partial-shipment::partial-shipment.history.created', [
                'code' => $partialShipment->partial_shipment_code,
                'items' => $partialShipment->total_items,
            ]),
            'order_id' => $partialShipment->order_id,
            'user_id' => Auth::id(),
            'extras' => json_encode([
                'partial_shipment_id' => $partialShipment->id,
                'partial_shipment_code' => $partialShipment->partial_shipment_code,
                'total_items' => $partialShipment->total_items,
                'total_weight' => $partialShipment->total_weight,
            ]),
        ]);

        // TODO: Send notification to customer about partial shipment
        // This can be implemented based on your notification system
    }

    public function handlePartialShipmentStatusUpdated(PartialShipmentStatusUpdated $event): void
    {
        $partialShipment = $event->partialShipment;
        $oldStatus = $event->oldStatus;

        // Create order history entry
        OrderHistory::create([
            'action' => OrderHistoryActionEnum::UPDATE_SHIPPING_STATUS,
            'description' => trans('plugins/partial-shipment::partial-shipment.history.status_updated', [
                'code' => $partialShipment->partial_shipment_code,
                'old_status' => $oldStatus->label(),
                'new_status' => $partialShipment->status->label(),
            ]),
            'order_id' => $partialShipment->order_id,
            'user_id' => Auth::id(),
            'extras' => json_encode([
                'partial_shipment_id' => $partialShipment->id,
                'partial_shipment_code' => $partialShipment->partial_shipment_code,
                'old_status' => $oldStatus->value,
                'new_status' => $partialShipment->status->value,
                'tracking_id' => $partialShipment->tracking_id,
                'shipping_company_name' => $partialShipment->shipping_company_name,
            ]),
        ]);

        // TODO: Send notification to customer about status update
        // This can be implemented based on your notification system
    }
}
