<x-core::button type="button" class="btn btn-info btn-trigger-partial-payment" data-payment-id="{{ $order->payment->id }}" data-order-id="{{ $order->id }}" data-remaining="{{ format_price($order->amount - $paidAmount) }}" data-payment-method="{{ $order->payment->payment_channel->label() }}">
    {{ trans('plugins/partial-payment::partial-payment.add_partial_payment') }}
</x-core::button>

@push('footer')
<div class="modal fade" id="partial-payment-modal" tabindex="-1" role="dialog" aria-labelledby="partial-payment-modal-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="partial-payment-modal-label">{{ trans('plugins/partial-payment::partial-payment.add_partial_payment') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ trans('core/base::forms.close') }}">
                </button>
            </div>
            <div class="modal-body">
                <form id="partial-payment-form">
                    <input type="hidden" name="order_id" id="partial-payment-order-id">
                    <input type="hidden" name="payment_id" id="partial-payment-payment-id">

                    <div class="form-group">
                        <label for="amount">{{ trans('plugins/partial-payment::partial-payment.amount') }}</label>
                        <div class="input-group">
                            <input type="number" step="0.01" class="form-control" id="partial-payment-amount" name="amount" required>
                            <div class="input-group-append">
                                <span class="input-group-text">{{ get_application_currency()->title }}</span>
                            </div>
                        </div>
                        <small class="form-text text-muted">{{ trans('plugins/partial-payment::partial-payment.remaining') }}: <span id="remaining-amount"></span></small>
                    </div>

                    <div class="form-group">
                        <label for="payment_method">{{ trans('plugins/partial-payment::partial-payment.payment_method') }}</label>
                        <select class="form-control" id="partial-payment-method" name="payment_method" required>
                            @foreach (\Shaqi\Payment\Enums\PaymentMethodEnum::values() as $method)
                                <option value="{{ $method }}">{{ \Shaqi\Payment\Enums\PaymentMethodEnum::getLabel($method) }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">{{ trans('plugins/partial-payment::partial-payment.description') }}</label>
                        <textarea class="form-control" id="partial-payment-description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('core/base::forms.cancel') }}</button>
                <button type="button" class="btn btn-primary" id="partial-payment-submit">{{ trans('plugins/partial-payment::partial-payment.add') }}</button>
            </div>
        </div>
    </div>
</div>
@endpush
