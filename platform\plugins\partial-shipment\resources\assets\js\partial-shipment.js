class PartialShipmentManager {
    constructor() {
        this.currentOrderId = null;
        this.orderItems = [];
        this.partialShipments = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPartialShipments();
    }

    bindEvents() {
        // Create partial shipment button
        $(document).on('click', '.btn-create-partial-shipment', (e) => {
            e.preventDefault();
            this.openCreateModal();
        });

        // Select all items checkbox
        $(document).on('change', '#select-all-items', (e) => {
            const isChecked = e.target.checked;
            $('.item-checkbox').prop('checked', isChecked);
            this.updateQuantityInputs();
        });

        // Individual item checkbox
        $(document).on('change', '.item-checkbox', () => {
            this.updateSelectAllCheckbox();
            this.updateQuantityInputs();
        });

        // Quantity input change
        $(document).on('input', '.qty-to-ship-input', (e) => {
            this.validateQuantityInput(e.target);
        });

        // Create partial shipment form submission
        $(document).on('click', '#confirm-create-partial-shipment-button', (e) => {
            e.preventDefault();
            this.createPartialShipment();
        });

        // Update status button
        $(document).on('click', '.btn-update-status', (e) => {
            e.preventDefault();
            const shipmentId = $(e.currentTarget).closest('.partial-shipment-item').data('shipment-id');
            this.openUpdateStatusModal(shipmentId);
        });

        // Update status form submission
        $(document).on('click', '#confirm-update-partial-shipment-status-button', (e) => {
            e.preventDefault();
            this.updatePartialShipmentStatus();
        });

        // Cancel shipment button
        $(document).on('click', '.btn-cancel-shipment', (e) => {
            e.preventDefault();
            const shipmentId = $(e.currentTarget).closest('.partial-shipment-item').data('shipment-id');
            this.cancelPartialShipment(shipmentId);
        });
    }

    openCreateModal() {
        this.currentOrderId = window.orderData?.id || $('#order-id').val();
        if (!this.currentOrderId) {
            Shaqi.showError('Order ID not found');
            return;
        }

        $('#partial-shipment-order-id').val(this.currentOrderId);
        this.loadOrderItems();
        $('#create-partial-shipment-modal').modal('show');
    }

    loadOrderItems() {
        const $tbody = $('#partial-shipment-items-tbody');
        $tbody.html('<tr><td colspan="6" class="text-center">Loading...</td></tr>');

        $httpClient.make()
            .get(route('partial-shipments.order-items'), {
                order_id: this.currentOrderId
            })
            .then(({ data }) => {
                this.orderItems = data.items;
                this.renderOrderItems();
            })
            .catch((error) => {
                console.error(error);
                $tbody.html('<tr><td colspan="6" class="text-center text-danger">Failed to load items</td></tr>');
            });
    }

    renderOrderItems() {
        const $tbody = $('#partial-shipment-items-tbody');
        $tbody.empty();
        console.log('Rendering order items:', this.orderItems);

        this.orderItems.forEach((item, index) => {


            if (item.qty_remaining <= 0) return; // Skip fully shipped items

            const row = `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input item-checkbox"
                               data-index="${index}" value="${item.id}">
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            ${item.product_image ? `<img src="${item.product_image}" alt="" width="40" height="40" class="me-2" style="object-fit: cover; border-radius: 4px;">` : ''}
                            <div>
                                <div class="fw-medium">${item.product_name}</div>
                                ${item.product_options ? `<small class="text-muted">${this.formatProductOptions(item.product_options)}</small>` : ''}
                            </div>
                        </div>
                    </td>
                    <td class="text-center">${item.qty_ordered}</td>
                    <td class="text-center">${item.qty_shipped}</td>
                    <td class="text-center">${item.qty_remaining}</td>
                    <td>
                        <input type="number" class="form-control qty-to-ship-input"
                               data-index="${index}"
                               min="1"
                               max="${item.qty_remaining}"
                               value="1"
                               disabled>
                        <input type="hidden" name="items[${index}][order_product_id]" value="${item.id}">
                    </td>
                </tr>
            `;
            $tbody.append(row);
        });
    }

    updateSelectAllCheckbox() {
        const totalCheckboxes = $('.item-checkbox').length;
        const checkedCheckboxes = $('.item-checkbox:checked').length;

        $('#select-all-items').prop('checked', totalCheckboxes === checkedCheckboxes);
    }

    updateQuantityInputs() {
        $('.item-checkbox').each((index, checkbox) => {
            const $checkbox = $(checkbox);
            const $qtyInput = $(`.qty-to-ship-input[data-index="${$checkbox.data('index')}"]`);

            if ($checkbox.is(':checked')) {
                $qtyInput.prop('disabled', false);
            } else {
                $qtyInput.prop('disabled', true).val(1);
            }
        });
    }

    validateQuantityInput(input) {
        const $input = $(input);
        const index = $input.data('index');
        const maxQty = this.orderItems[index]?.qty_remaining || 0;
        const value = parseInt($input.val()) || 0;

        if (value > maxQty) {
            $input.val(maxQty);
            Shaqi.showError(`Maximum quantity for this item is ${maxQty}`);
        } else if (value < 1) {
            $input.val(1);
        }
    }

    createPartialShipment() {
        const formData = new FormData(document.getElementById('create-partial-shipment-form'));

        // Collect selected items
        const items = [];
        $('.item-checkbox:checked').each((index, checkbox) => {
            const $checkbox = $(checkbox);
            const itemIndex = $checkbox.data('index');
            const $qtyInput = $(`.qty-to-ship-input[data-index="${itemIndex}"]`);

            items.push({
                order_product_id: $checkbox.val(),
                qty_shipped: parseInt($qtyInput.val()) || 1
            });
        });

        if (items.length === 0) {
            Shaqi.showError('Please select at least one item to ship');
            return;
        }

        // Add items to form data
        formData.delete('items'); // Clear any existing items
        items.forEach((item, index) => {
            formData.append(`items[${index}][order_product_id]`, item.order_product_id);
            formData.append(`items[${index}][qty_shipped]`, item.qty_shipped);
        });

        const $button = $('#confirm-create-partial-shipment-button');

        $httpClient.make()
            .withButtonLoading($button)
            .post(route('partial-shipments.store'), formData)
            .then(({ data }) => {
                Shaqi.showSuccess('Partial shipment created successfully');
                $('#create-partial-shipment-modal').modal('hide');
                this.loadPartialShipments();

                // Reload the page to update order status
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch((error) => {
                const message = error.response?.data?.message || 'Failed to create partial shipment';
                Shaqi.showError(message);
            });
    }

    loadPartialShipments() {
        const orderId = window.orderData?.id || $('#order-id').val();
        if (!orderId) return;

        $httpClient.make()
            .get(route('partial-shipments.by-order', orderId))
            .then(({ data }) => {
                this.partialShipments = data;
                this.renderPartialShipments();
            })
            .catch((error) => {
                console.error(error);
            });
    }

    renderPartialShipments() {
        const $container = $('#partial-shipments-list');
        const $noShipments = $('#no-partial-shipments');

        if (this.partialShipments.length === 0) {
            $container.hide();
            $noShipments.show();
            return;
        }

        $noShipments.hide();
        $container.empty().show();

        this.partialShipments.forEach(shipment => {
            const $template = $('#partial-shipment-template').contents().clone();

            // Populate shipment data
            $template.find('.shipment-code').text(shipment.partial_shipment_code);
            $template.find('.shipment-status').html(this.getStatusBadge(shipment.status));
            $template.find('.created-at').text(this.formatDate(shipment.created_at));
            $template.find('.total-items').text(shipment.items.reduce((sum, item) => sum + item.qty_shipped, 0));
            $template.find('.total-weight').text(`${shipment.total_weight} kg`);

            // Set shipment ID
            $template.find('.partial-shipment-item').attr('data-shipment-id', shipment.id);

            // Show optional fields if they have values
            if (shipment.tracking_id) {
                $template.find('.tracking-info').show();
                $template.find('.tracking-id').text(shipment.tracking_id);

                if (shipment.tracking_link) {
                    $template.find('.tracking-link').attr('href', shipment.tracking_link).show();
                }
            }

            if (shipment.shipping_company_name) {
                $template.find('.shipping-company').show();
                $template.find('.company-name').text(shipment.shipping_company_name);
            }

            if (shipment.date_shipped) {
                $template.find('.date-shipped').show();
                $template.find('.shipped-date').text(this.formatDate(shipment.date_shipped));
            }

            if (shipment.note) {
                $template.find('.shipment-note').show();
                $template.find('.note-text').text(shipment.note);
            }

            if (shipment.shipping_cost > 0) {
                $template.find('.shipping-cost').show();
                $template.find('.cost-amount').text(this.formatCurrency(shipment.shipping_cost));
            }

            // Populate shipped items
            const $itemsList = $template.find('.shipped-items-list');
            shipment.items.forEach(item => {
                const $itemTemplate = $('#shipped-item-template').contents().clone();

                $itemTemplate.find('.item-image').attr('src', item.product_image || '/vendor/core/core/base/images/placeholder.png');
                $itemTemplate.find('.item-name').text(item.product_name);
                $itemTemplate.find('.item-options').text(this.formatProductOptions(item.product_options));
                $itemTemplate.find('.item-quantity').text(item.qty_shipped);
                $itemTemplate.find('.item-weight').text(`${item.weight * item.qty_shipped} kg`);

                $itemsList.append($itemTemplate);
            });

            // Hide action buttons for completed/canceled shipments
            if (['delivered', 'canceled'].includes(shipment.status)) {
                $template.find('.shipment-actions').hide();
            }

            $container.append($template);
        });
    }

    openUpdateStatusModal(shipmentId) {
        const shipment = this.partialShipments.find(s => s.id === shipmentId);
        if (!shipment) return;

        const $form = $('#update-partial-shipment-status-form');
        $form[0].reset();

        $('#update-partial-shipment-id').val(shipmentId);
        $form.find('[name="status"]').val(shipment.status);
        $form.find('[name="tracking_id"]').val(shipment.tracking_id || '');
        $form.find('[name="shipping_company_name"]').val(shipment.shipping_company_name || '');
        $form.find('[name="tracking_link"]').val(shipment.tracking_link || '');

        if (shipment.date_shipped) {
            $form.find('[name="date_shipped"]').val(this.formatDateTimeLocal(shipment.date_shipped));
        }

        $('#update-partial-shipment-status-modal').modal('show');
    }

    updatePartialShipmentStatus() {
        const shipmentId = $('#update-partial-shipment-id').val();
        const formData = new FormData(document.getElementById('update-partial-shipment-status-form'));
        const $button = $('#confirm-update-partial-shipment-status-button');

        $httpClient.make()
            .withButtonLoading($button)
            .put(route('partial-shipments.update-status', shipmentId), formData)
            .then(({ data }) => {
                Shaqi.showSuccess('Partial shipment status updated successfully');
                $('#update-partial-shipment-status-modal').modal('hide');
                this.loadPartialShipments();
            })
            .catch((error) => {
                const message = error.response?.data?.message || 'Failed to update status';
                Shaqi.showError(message);
            });
    }

    cancelPartialShipment(shipmentId) {
        if (!confirm('Are you sure you want to cancel this partial shipment? This action cannot be undone.')) {
            return;
        }

        $httpClient.make()
            .delete(route('partial-shipments.destroy', shipmentId))
            .then(({ data }) => {
                Shaqi.showSuccess('Partial shipment canceled successfully');
                this.loadPartialShipments();

                // Reload the page to update order status
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch((error) => {
                const message = error.response?.data?.message || 'Failed to cancel partial shipment';
                Shaqi.showError(message);
            });
    }

    // Utility methods
    formatProductOptions(options) {
        if (!options || typeof options !== 'object') return '';

        return Object.entries(options)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleString();
    }

    formatDateTimeLocal(dateString) {
        const date = new Date(dateString);
        return date.toISOString().slice(0, 16);
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: window.siteConfig?.currency || 'USD'
        }).format(amount);
    }

    getStatusBadge(status) {
        const badges = {
            pending: '<span class="badge bg-warning">Pending</span>',
            processing: '<span class="badge bg-info">Processing</span>',
            shipped: '<span class="badge bg-primary">Shipped</span>',
            delivered: '<span class="badge bg-success">Delivered</span>',
            canceled: '<span class="badge bg-danger">Canceled</span>'
        };

        return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
    }
}

// Initialize when document is ready
$(document).ready(() => {
    if (typeof window.partialShipmentManager === 'undefined') {
        window.partialShipmentManager = new PartialShipmentManager();
    }
});
