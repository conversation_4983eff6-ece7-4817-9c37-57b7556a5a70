(()=>{"use strict";$((function(){$(document).on("click",".btn-trigger-cleanup",(function(n){n.preventDefault(),$("#cleanup-modal").modal("show")})),$(document).on("click","#cleanup-submit-action",(function(n){n.preventDefault(),n.stopPropagation();var t=$(n.currentTarget);Shaqi.showButtonLoading(t);var a=$("#form-cleanup-database"),o=$("#cleanup-modal");$httpClient.make().post(a.prop("action"),new FormData(a[0])).then((function(n){var t=n.data;return Shaqi.showSuccess(t.message)})).finally((function(){Shaqi.hideButtonLoading(t),o.modal("hide")}))}))}))})();
//# sourceMappingURL=cleanup.js.map