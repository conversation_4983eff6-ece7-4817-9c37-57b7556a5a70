<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('ec_partial_shipment_items', function (Blueprint $table): void {
            $table->id();
            $table->unsignedBigInteger('partial_shipment_id');
            $table->unsignedBigInteger('order_product_id');
            $table->unsignedBigInteger('product_id');
            $table->string('product_name');
            $table->string('product_image')->nullable();
            $table->integer('qty_shipped');
            $table->decimal('weight', 8, 2)->default(0);
            $table->decimal('price', 15, 2)->default(0);
            $table->json('product_options')->nullable();
            $table->timestamps();

            $table->foreign('partial_shipment_id')->references('id')->on('ec_partial_shipments')->onDelete('cascade');
            $table->foreign('order_product_id')->references('id')->on('ec_order_product')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('ec_products')->onDelete('cascade');
            $table->index(['partial_shipment_id', 'product_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ec_partial_shipment_items');
    }
};
