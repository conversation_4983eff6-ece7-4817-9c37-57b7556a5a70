<?php

namespace Shaqi\ACL\Providers;

use <PERSON><PERSON>qi\ACL\Events\RoleAssignmentEvent;
use Shaqi\ACL\Events\RoleUpdateEvent;
use <PERSON><PERSON>qi\ACL\Listeners\LoginListener;
use <PERSON>haqi\ACL\Listeners\RoleAssignmentListener;
use Shaqi\ACL\Listeners\RoleUpdateListener;
use Illuminate\Auth\Events\Login;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        RoleUpdateEvent::class => [
            RoleUpdateListener::class,
        ],
        RoleAssignmentEvent::class => [
            RoleAssignmentListener::class,
        ],
        Login::class => [
            LoginListener::class,
        ],
    ];
}
