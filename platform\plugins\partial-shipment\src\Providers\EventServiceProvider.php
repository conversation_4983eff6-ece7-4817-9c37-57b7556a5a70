<?php

namespace Shaqi\PartialShipment\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Shaqi\PartialShipment\Events\PartialShipmentCreated;
use Shaqi\PartialShipment\Events\PartialShipmentStatusUpdated;
use Shaqi\PartialShipment\Listeners\PartialShipmentEventListener;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        PartialShipmentCreated::class => [
            [PartialShipmentEventListener::class, 'handlePartialShipmentCreated']
        ],
        PartialShipmentStatusUpdated::class => [
            [PartialShipmentEventListener::class, 'handlePartialShipmentStatusUpdated']
        ],
    ];
}
