<?php

namespace Shaqi\PartialPayment\Http\Controllers;

use Shaqi\Base\Http\Controllers\BaseController;
use <PERSON>haqi\Base\Http\Responses\BaseHttpResponse;
use <PERSON>haqi\Ecommerce\Models\Order;
use Shaqi\Payment\Enums\PaymentStatusEnum;
use Shaqi\Payment\Models\Payment;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Auth;
use Shaqi\Ecommerce\Enums\OrderHistoryActionEnum;
use Shaqi\Ecommerce\Models\OrderHistory;
use Shaqi\PartialPayment\Events\PartialPaymentAdded;

class PartialPaymentController extends BaseController
{
    public function store(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->validate([
            'order_id' => 'required|exists:ec_orders,id',
            'payment_method' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        $userId = Auth::id();

        $order = Order::findOrFail($request->input('order_id'));

        $amount = $request->input('amount');

        $paidAmount = Payment::query()
            ->where('order_id', $order->id)
            ->where('status', PaymentStatusEnum::COMPLETED)->whereNotIn('id', [$order->payment->id])
            ->sum('amount');

        // Check if amount is valid
        $remainingAmount = $order->amount - $paidAmount;

        if ($amount > $remainingAmount) {
            return $response
                ->setError()
                ->setMessage(__('The partial payment amount cannot exceed the remaining amount: :amount', ['amount' => format_price($remainingAmount)]));
        }

        try {
            // create payment record
            $payment = new Payment([
                'order_id' => $order->id,
                'charge_id' => 'partial-' . time(),
                'amount' => $amount,
                'currency' => $order->currency_id,
                'payment_channel' => $request->input('payment_method'),
                'status' => PaymentStatusEnum::COMPLETED,
                'payment_type' => 'partial',
                'customer_id' => $order->user_id,
                'customer_type' => get_class($order->user),
                'description' => $request->input('description') ?: 'Partial payment for order #' . $order->code,
            ]);

            $payment->save();

            // Check if order is fully paid
            $totalPaid = Payment::query()
                ->where('order_id', $order->id)
                ->where('status', PaymentStatusEnum::COMPLETED)->whereNotIn('id', [$order->payment->id])
                ->sum('amount');

            if ($totalPaid >= $order->amount) {
                // Update order payment status
                $order->payment->status = PaymentStatusEnum::COMPLETED;
                $order->payment->amount = $totalPaid;
                $order->payment->save();
            } else {
                // Update order payment status
                $order->payment->amount = $totalPaid;
                $order->payment->status = PaymentStatusEnum::PARTIAL;
                $order->payment->save();
            }

            // Add order History
            OrderHistory::query()->create([
                'action' => OrderHistoryActionEnum::CONFIRM_PARTIAL_PAYMENT,
                'description' => __('Partial payment added of amount :amount', ['amount' => format_price($amount)]),
                'order_id' => $order->getKey(),
                'user_id' => $userId,
            ]);

            // Dispatch event for email notification
            event(new PartialPaymentAdded($order, $payment));

            return $response
                ->setMessage(__('Added partial payment successfully!'))
                ->setData([
                    'next_url' => route('orders.edit', $order->id),
                ])
            ;
        } catch (Exception $e) {

            return $response
                ->setError()
                ->setMessage($e->getMessage());
        }
    }
}

